#include <ctype.h>
#include <errno.h>
#include <linux/init.h>
#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/string.h>
#include <linux/types.h>
#include <linux/unaligned/be_byteshift.h>
#include <stdio.h>
#include <stdlib.h>
#undef _KERNEL
#include <errno.h>
#include <pflash.h>
#include <pmon.h>
#include <sys/types.h>
#include <target/ls2k500.h>
#include <target/spi-io.h>

#include "tcm2_internal.h"
#include "tcm2_tis.h"

extern int ls_spi_transfer_poll(struct message *msg);

#define MAX_SPI_FRAMESIZE 64
#define TCM_HEADER_SIZE 10

/* Max buffer size supported by our tcm */
#define TCM_DEV_BUFSIZE 4096

#define TCM_PCR_MINIMUM_DIGEST_SIZE 20

/* Number of wait states to wait for */
#define TCM_WAIT_STATES 100

struct tcm_chip tcm330 = {
    .is_open = 0,
    .locality = 0,
    .vend_dev = 0,
    .rid = 0,
    .timeout_a = 0,
    .timeout_b = 0,
    .timeout_c = 0,
    .timeout_d = 0,
    .chip_type = 0,
};

struct tcm_chip *chip = &tcm330;

static int tcm_tis_spi_transfer(u32 addr, u16 len, const u8 *in, u8 *out) {
    int transfer_len, ret;
    struct message m;
    u8 tx_buf[MAX_SPI_FRAMESIZE];
    u8 rx_buf[MAX_SPI_FRAMESIZE];

    if(in && out) {
        tcm_err("%s: can't do full duplex\n", __func__);
        return -EINVAL;
    }

    while(len) {
        memset(&m, 0, sizeof(m));
        transfer_len = min_t(u16, len, MAX_SPI_FRAMESIZE);
        tx_buf[0] = (in ? 0x80 : 0) | (transfer_len - 1);
        tx_buf[1] = 0xD4;
        tx_buf[2] = addr >> 8;
        tx_buf[3] = addr;

        m.tx_buf = tx_buf;
        m.rx_buf = rx_buf;
        m.len = 4;
        m.cs_change = 1;

        ret = ls_spi_transfer_poll(&m);
        if(ret < 0) {
            goto release_bus;
        }

        if(!(rx_buf[3] & 0x1)) {
            int i;
            m.tx_buf = NULL;

            for(i = 0; i < TCM_WAIT_STATES; i++) {
                m.len = 1;
                ret = ls_spi_transfer_poll(&m);
                if(ret) {
                    goto release_bus;
                }

                if(rx_buf[0] & 0x1)
                    break;
            }

            if(i == TCM_WAIT_STATES) {
                ret = -ETIMEDOUT;
                goto release_bus;
            }
        }

        m.cs_change = 0;
        m.len = transfer_len;

        if(in) {
            m.tx_buf = NULL;
        } else {
            m.rx_buf = NULL;
            memcpy(tx_buf, out, transfer_len);
            out += transfer_len;
        }

        ret = ls_spi_transfer_poll(&m);
        if(ret) {
            goto release_bus;
        }

        if(in) {
            memcpy(in, rx_buf, transfer_len);
            in += transfer_len;
        }

        len -= transfer_len;
    }

release_bus:
    return ret;
}

static int tcm_tis_spi_read_bytes(u32 addr,
                                  u16 len, u8 *result) {
    return tcm_tis_spi_transfer(addr, len, result, NULL);
}

static int tcm_tis_spi_write_bytes(u32 addr,
                                   u16 len, const u8 *value) {
    return tcm_tis_spi_transfer(addr, len, NULL, value);
}

static int tcm_tis_spi_read16(u32 addr, u16 *result) {
    __le16 result_le;
    int rc;

    rc = tcm_tis_spi_read_bytes(addr, sizeof(u16),
                                (u8 *)&result_le);
    if(!rc)
        *result = le16_to_cpu(result_le);

    return rc;
}

static int tcm_tis_spi_read32(u32 addr, u32 *result) {
    __le32 result_le;
    int rc;

    rc = tcm_tis_spi_read_bytes(addr, sizeof(u32),
                                (u8 *)&result_le);
    if(!rc)
        *result = le32_to_cpu(result_le);

    return rc;
}

static int tcm_tis_spi_write32(u32 addr, u32 value) {
    __le32 value_le;
    int rc;

    value_le = cpu_to_le32(value);
    rc = tcm_tis_spi_write_bytes(addr, sizeof(u32),
                                 (u8 *)&value_le);

    return rc;
}

static int tcm_tis_spi_check_locality(int loc) {
    u8 locality;
    int ret;

    ret = tcm_tis_spi_read_bytes(TCM_ACCESS(loc), 1, &locality);
    if(ret)
        return ret;
    if((locality & (TCM_ACCESS_ACTIVE_LOCALITY | TCM_ACCESS_VALID | TCM_ACCESS_REQUEST_USE)) ==
       (TCM_ACCESS_ACTIVE_LOCALITY | TCM_ACCESS_VALID)) {
        chip->locality = loc;
        return 0;
    }
    return -ENOENT;
    ;
}

static void tcm_tis_spi_release_locality(int loc,
                                         char force) {
    const u8 mask = TCM_ACCESS_REQUEST_PENDING | TCM_ACCESS_VALID;
    u8 buf;

    if(tcm_tis_spi_read_bytes(TCM_ACCESS(loc), 1, &buf) < 0)
        return;

    if(force || (buf & mask) == mask) {
        buf = TCM_ACCESS_ACTIVE_LOCALITY;
        tcm_tis_spi_write_bytes(TCM_ACCESS(loc), 1, &buf);
    }
}

static int tcm_tis_spi_request_locality(int loc) {
    unsigned long stop;
    u8 buf = TCM_ACCESS_REQUEST_USE;
    int ret;

    ret = tcm_tis_spi_check_locality(loc);
    if(!ret)
        return 0;

    if(ret != -ENOENT) {
        printf("%s: Failed to get locality: %d\n",
               __func__, ret);
        return ret;
    }

    ret = tcm_tis_spi_write_bytes(TCM_ACCESS(loc), 1, &buf);
    if(ret) {
        tcm_err("%s: Failed to write to TCM: %d\n",
               __func__, ret);
        return ret;
    }

    stop = chip->timeout_a;
    do {
        ret = tcm_tis_spi_check_locality(loc);
        if(!ret)
            return 0;

        if(ret != -ENOENT) {
            tcm_err("%s: Failed to get locality: %d\n", __func__, ret);
            return ret;
        }

        mdelay(TCM_TIMEOUT_MS);
    } while(stop--);

    printf("%s: Timeout getting locality: %d\n", __func__, ret);

    return ret;
}

static u8 tcm_tis_spi_status(u8 *status) {
    return tcm_tis_spi_read_bytes(TCM_STS(chip->locality), 1, status);
}

static int tcm_tis_spi_wait_for_stat(u8 mask,
                                     unsigned long timeout, u8 *status) {
    unsigned long stop = timeout;
    int ret;

    do {
        mdelay(TCM_TIMEOUT_MS);
        ret = tcm_tis_spi_status(status);
        if(ret)
            return ret;

        if((*status & mask) == mask)
            return 0;
    } while(stop--);

    return -ETIMEDOUT;
}

static u8 tcm_tis_spi_valid_status(u8 *status) {
    return tcm_tis_spi_wait_for_stat(TCM_STS_VALID,
                                     chip->timeout_c, status);
}

static int tcm_tis_spi_get_burstcount(void) {
    unsigned long stop;
    u32 burstcount, ret;

    /* wait for burstcount */
    stop = chip->timeout_d;
    do {
        ret = tcm_tis_spi_read32(TCM_STS(chip->locality),
                                 &burstcount);
        if(ret)
            return -EBUSY;

        burstcount = (burstcount >> 8) & 0xFFFF;
        if(burstcount)
            return burstcount;

        mdelay(TCM_TIMEOUT_MS);
    } while(stop--);

    return -EBUSY;
}

static int tcm_tis_spi_cancel(void) {
    u8 data = TCM_STS_COMMAND_READY;

    return tcm_tis_spi_write_bytes(TCM_STS(chip->locality), 1, &data);
}

static int tcm_tis_spi_recv_data(u8 *buf, size_t count) {
    int size = 0, burstcnt, len, ret;
    u8 status;

    while(size < count &&
          tcm_tis_spi_wait_for_stat(TCM_STS_DATA_AVAIL | TCM_STS_VALID,
                                    chip->timeout_c, &status) == 0) {
        burstcnt = tcm_tis_spi_get_burstcount();
        if(burstcnt < 0)
            return burstcnt;

        len = min_t(int, burstcnt, count - size);
        ret = tcm_tis_spi_read_bytes(TCM_DATA_FIFO(chip->locality),
                                     len, buf + size);
        if(ret < 0)
            return ret;

        size += len;
    }

    return size;
}

static int tcm_tis_spi_recv(u8 *buf, size_t count) {
    int size, expected;

    if(!chip)
        return -ENODEV;

    if(count < TCM_HEADER_SIZE) {
        size = -EIO;
        goto out;
    }

    size = tcm_tis_spi_recv_data(buf, TCM_HEADER_SIZE);
    if(size < TCM_HEADER_SIZE) {
        tcm_err("TCM error, unable to read header\n");
        goto out;
    }

    expected = get_unaligned_be32(buf + 2);
    if(expected > count) {
        size = -EIO;
        goto out;
    }

    size += tcm_tis_spi_recv_data(&buf[TCM_HEADER_SIZE],
                                  expected - TCM_HEADER_SIZE);
    if(size < expected) {
        tcm_err("TCM error, unable to read remaining bytes of result\n");
        size = -EIO;
        goto out;
    }

out:
    tcm_tis_spi_cancel();
    tcm_tis_spi_release_locality(chip->locality, false);

    return size;
}

static int tcm_tis_spi_send(const u8 *buf, size_t len) {
    u32 i, size;
    u8 status;
    int burstcnt, ret, count;
    u8 data;

    if(len > TCM_DEV_BUFSIZE)
        return -E2BIG; /* Command is too long for our tcm, sorry */

    ret = tcm_tis_spi_request_locality(0);
    if(ret < 0)
        return -EBUSY;

    /*
     * Check if the TCM is ready. If not, if not, cancel the pending command
     * and poll on the status to be finally ready.
     */
    ret = tcm_tis_spi_status(&status);
    if(ret)
        return ret;

    if(!(status & TCM_STS_COMMAND_READY)) {
        /* Force the transition, usually this will be done at startup */
        ret = tcm_tis_spi_cancel();
        if(ret) {
            tcm_err("%s: Could not cancel previous operation\n",
                   __func__);
            goto out_err;
        }

        ret = tcm_tis_spi_wait_for_stat(TCM_STS_COMMAND_READY,
                                        chip->timeout_b, &status);
        if(ret < 0 || !(status & TCM_STS_COMMAND_READY)) {
            tcm_err("status %d after wait for stat returned %d\n",
                   status, ret);
            goto out_err;
        }
    }

    for(i = 0; i < len;) {
        burstcnt = tcm_tis_spi_get_burstcount();
        if(burstcnt < 0)
            return burstcnt;
        size = min_t(int, len - i, burstcnt);

        ret = tcm_tis_spi_write_bytes(TCM_DATA_FIFO(chip->locality),
                                      size, buf + i);
        if(ret < 0)
            goto out_err;

        i += size;
    }

#if 0
	ret = tcm_tis_spi_valid_status(&status);
	if (ret)
		goto out_err;

	if ((status & TCM_STS_DATA_EXPECT) == 0) {
		ret = -EIO;
		goto out_err;
	}

       
        ret = tcm_tis_spi_write_bytes(TCM_DATA_FIFO(chip->locality),
                                1, buf + len - 1);
        if (ret)
                goto out_err;
#endif

    ret = tcm_tis_spi_valid_status(&status);
    if(ret)
        goto out_err;
    
    if((status & TCM_STS_DATA_EXPECT) != 0) {
        ret = -EIO;
        goto out_err;
    }

    data = TCM_STS_GO;
    ret = tcm_tis_spi_write_bytes(TCM_STS(chip->locality), 1, &data);
    if(ret)
        goto out_err;

    return len;

out_err:
    tcm_tis_spi_cancel();
    tcm_tis_spi_release_locality(chip->locality, false);

    return ret;
}

static int tcm_tis_spi_cleanup(void) {
    tcm_tis_spi_cancel();
    /*
     * The TCM needs some time to clean up here,
     * so we sleep rather than keeping the bus busy
     */
    mdelay(2);
    tcm_tis_spi_release_locality(chip->locality, false);

    return 0;
}

/*
 * Configure GPIO pin multiplexing for TCM SPI interface
 * Different platforms use different GPIO configurations for SPI pins
 */
static void tcm_configure_gpio_mux(void) {
#ifdef LOONGARCH_2P300
    /*
     * 2P0300开发板可信Z32H330TC
     * 硬件接口:使用SPI1
     * SPI1_MOSI 是 PRT_I2C_SCL 引脚复用;
     * SPI1_CS 是 PRT_I2C_SDA 引脚复用;
     * SPI1_CLK 是 SCA_PMIO0 复用;
     * SPI1_MISO 是 SCA_PMIO1 复用
     *
     * 参考：LS2P0300_um_内部参考版v0.2_NLC.pdf
     * 0x15202040：扫描系统 GPIO0~15 复用配置寄存器。  0x5000 =0101 0000 0000 0000    SCA_GPIO06(12:13) = 01; SCA_GPIO07(14:15) = 01
     * 0x15103040：打印系统 GPIO0~15 复用配置寄存器。  0x50 = 0101 0000   PRT_GPIO02(5:4) = 01; PRT_GPIO03(7:6) = 01
     *
     * 01 表示第一复用
     */
    writel(0x5000, PHYS_TO_UNCACHED(0x15202040));
    writel(0x50, PHYS_TO_UNCACHED(0x15103040));

#elif defined(TCM2) && defined(LOONGARCH_2P500_HANTU)
    /*
     * HANTU开发板可信TCM芯片
     * 硬件接口:使用SPI2 (prt_spi接口映射到SPI2控制器 0x15109000)
     * SPI2_CLK 是 prt_spi_clk 引脚复用 (PRT_GPIO06);
     * SPI2_MISO 是 prt_spi_miso 引脚复用 (PRT_GPIO07);
     * SPI2_MOSI 是 prt_spi_mosi 引脚复用 (PRT_GPIO08);
     * SPI2_CS 是 prt_spi_cs 引脚复用 (PRT_GPIO09);
     *
     * 寄存器地址：0x15103040 - 打印系统 GPIO0~15 复用配置寄存器
     * GPIO06_MUX (13:12) = 11: 引脚主功能 (prt_spi_clk)
     * GPIO07_MUX (15:14) = 11: 引脚主功能 (prt_spi_miso)
     * GPIO08_MUX (17:16) = 11: 引脚主功能 (prt_spi_mosi)
     * GPIO09_MUX (19:18) = 11: 引脚主功能 (prt_spi_cs)
     *
     * 配置值: 0xFF000 = 1111 1111 0000 0000 0000
     * 位19:18(GPIO09)=11, 位17:16(GPIO08)=11, 位15:14(GPIO07)=11, 位13:12(GPIO06)=11
     */
    writel(0xFF000, PHYS_TO_UNCACHED(0x15103040));

#else
    /* Default configuration or no specific GPIO mux needed */
    /* Add other platform configurations here as needed */
#endif
}

static int tcm_tis_spi_open(struct tcm_chip *chip) {
    if(chip->is_open)
        return -EBUSY;

    chip->is_open = 1;

    return 0;
}

static int tcm_tis_spi_close(void) {
    if(chip->is_open) {
        tcm_tis_spi_release_locality(chip->locality, true);
        chip->is_open = 0;
    }

    return 0;
}
int tcm_tis_get_desc(char *buf, int size) {
    if(size < 80)
        return -ENOSPC;

    return snprintf(buf, size,
                    "tcm2_tis v2.0: VendorID 0x%04x, DeviceID 0x%04x, RevisionID 0x%02x [%s]",
                    chip->vend_dev & 0xFFFF,
                    chip->vend_dev >> 16, chip->rid,
                    (chip->is_open ? "open" : "closed"));
}

static int tcm_tis_wait_init(int loc) {
    unsigned long stop;
    u8 status;
    int ret;

    stop = chip->timeout_b;
    do {
        mdelay(TCM_TIMEOUT_MS);

        ret = tcm_tis_spi_read_bytes(TCM_ACCESS(loc), 1, &status);
        if(ret)
            break;
        if(status & TCM_ACCESS_VALID)
            return 0;
    } while(stop--);

    return -EIO;
}

int tcm_tis_init(void) {
    int ret;
    chip->locality = 0;
    chip->timeout_a = TIS_SHORT_TIMEOUT_MS;
    chip->timeout_b = TIS_LONG_TIMEOUT_MS;
    chip->timeout_c = TIS_SHORT_TIMEOUT_MS;
    chip->timeout_d = TIS_SHORT_TIMEOUT_MS;

    /* Configure GPIO pin multiplexing for TCM SPI interface based on platform */
    tcm_configure_gpio_mux();

    ret = tcm_tis_wait_init(chip->locality);
    if(ret) {
        tcm_err("%s: no device found\n", __func__);
        return ret;
    }

    ret = tcm_tis_spi_request_locality(chip->locality);
    if(ret) {
        tcm_err("%s: could not request locality %d\n",
               __func__, chip->locality);
        return ret;
    }

    ret = tcm_tis_spi_read32(TCM_DID_VID(0),
                             &chip->vend_dev);
    if(ret) {
        tcm_err("%s: could not retrieve VendorID/DeviceID\n", __func__);
        return ret;
    }

    ret = tcm_tis_spi_read_bytes(TCM_RID(0), 1, &chip->rid);
    if(ret) {
        tcm_err("%s: could not retrieve RevisionID\n",
               __func__);
        return ret;
    }

    printf("SPI TCMv2.0 found (vid:%04x, did:%04x, rid:%02d)\n",
           chip->vend_dev & 0xFFFF, chip->vend_dev >> 16, chip->rid);

    return 0;
}

static int tcm_tis_spi_remove(void) {
    tcm_tis_spi_release_locality(chip->locality, true);

    return 0;
}

int tcm_xfer(const uint8_t *sendbuf, size_t send_size,
             uint8_t *recvbuf, size_t *recv_size) {
    ulong stop;
    uint count, ordinal;
    int ret, ret2 = 0;
    u8 buf[TCM_DEV_BUFSIZE + sizeof(u8)];

    count = get_unaligned_be32(sendbuf + TCM_CMD_COUNT_BYTE);
    ordinal = get_unaligned_be32(sendbuf + TCM_CMD_ORDINAL_BYTE);

    if(count == 0) {
        tcm_err("no data\n");
        return -ENODATA;
    }
    if(count > send_size) {
        tcm_err("invalid count value %x %zx\n", count, send_size);
        return -E2BIG;
    }

    ret = tcm_tis_spi_send(sendbuf, send_size);
    if(ret < 0) {
        tcm_err("%s: send erring %d\n", __func__, ret);
        return ret;
    }

    stop = 0xffffff;
    do {
        ret = tcm_tis_spi_recv(buf, sizeof(buf));
        if(ret >= 0) {
            if(ret > *recv_size) {
                tcm_err("%s: recv size erring %d\n", __func__, ret);
                return -ENOSPC;
            }
            memcpy(recvbuf, buf, ret);
            *recv_size = ret;
            ret = 0;
            break;
        } else if(ret != -EAGAIN) {
            tcm_err("%s: recv failed with error: %d\n", __func__, ret);
            return ret;
        }

        stop--;
        mdelay(TCM_TIMEOUT_MS);
        if(!stop) {
            ret = -ETIMEDOUT;
            tcm_err("%s: timed out\n", __func__);
            break;
        }
    } while(ret);

    if(ret) {
        tcm_tis_spi_cleanup();
        tcm_err("xfer: returning err:%d\n", ret);
        return ret;
    }

    // Debug command
/*     printf("Response Data[size:%zu]: \n", *recv_size);
    for(size_t i = 0; i < *recv_size; i++) {
        printf("%02x", recvbuf[i]);
        if((i + 1) % 2 == 0)
            printf(" ");
        if((i + 1) % 16 == 0)
            printf("\n");
        else if(i == *recv_size - 1)
            printf("\n");
    }
    printf("\n"); */
    return 0;
}

/*
 * TCM automatic startup function
 * This function combines tcm_tis_init and tcm2_startup for convenient initialization
 * Should be called before any TCM operations
 * Includes status check to avoid duplicate initialization
 */
int tcm2_autostart(void) {
    int ret;

    // Check if TCM is already initialized
    if (chip->is_open) {
        printf("TCM device is already initialized\n");
        return 0;
    }

    printf("Initializing TCM device...\n");

    // Initialize TCM TIS interface
    ret = tcm_tis_init();
    if (ret != 0) {
        printf("TCM TIS initialization failed, error code: %d\n", ret);
        return ret;
    }

    // Start TCM with clear mode (reset state)
    ret = tcm2_startup(TCM_SU_CLEAR);
    if (ret != 0) {
        printf("TCM startup failed, error code: %d\n", ret);
        return ret;
    }

    // Mark TCM as open/ready
    chip->is_open = 1;

    printf("TCM device initialized successfully\n");
    return 0;
}

/*
 * Check TCM initialization status
 * Returns 1 if TCM is initialized and ready, 0 otherwise
 */
int tcm2_is_ready(void) {
    return chip->is_open;
}

static const Cmd Cmds[] =
    {
        {"MyCmds"},
        {"tcm_init", "", 0, "tcm init", tcm_tis_init, 0, 99, CMD_REPEAT},
        {"tcm2_autostart", "", 0, "tcm automatic startup", tcm2_autostart, 0, 99, CMD_REPEAT},
        {"tcm_remove", "", 0, "tcm remove", tcm_tis_spi_remove, 0, 99, CMD_REPEAT},
        {0, 0}};

static void init_cmd __P((void)) __attribute__((constructor));

static void init_cmd() {
    cmdlist_expand(Cmds, 1);
}
