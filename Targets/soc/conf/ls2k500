# $Id: <PERSON><PERSON>,v 1.1.1.1 2006/09/14 01:59:09 root Exp $ # #	GENERIC configuration for Galileo EV64240 # #  This file is supposed to be included by target file after #  endian has been defined.
#
machine		soc		loongarch	ls2k500		# CPU Architecture, Platform, CPU name
select		loongarch
config		pmon
option		VGAROM_IN_BIOS

option		LOONGSON_2K
option		LOONGARCH_2K500
option		BOOT_PARAM
option		VRAM_SIZE=16
option		SOC_CPU
#
#  Define target endian
#
makeoptions	ENDIAN=EL				# Little endian version.

option		BOOT_PARAMS_BPI				# for 4.19 kernel
#option		BOOT_PARAMS_EFI				# for older kernel

#include "conf/GENERIC_ALL"

#
# System Name and Target Name
#
option		SYSTYPE="\"loongson\""
option		TARGETNAME="\"loongson\""

#
# System str options
#
#option		LS_STR

#
# Platform options
#
option		BONITOEL
option		DEVBD2E
option		INET
option		LS3_HT					# Enable the IO cache coherent for ls2k

option		loongson3A3
option		LSMC_2
#option		ARB_LEVEL
option		DDR3_DIMM
option		TOT_NODE_NUM=0

#<<<<<<<<<<<<<<
###these options can NOT defined at the same time
#option		DDR3_SODIMM #1Rx8 2GB
#option		DDR3_SMT    #for SMT x16 SDRAM 4 chip 2GB
#>>>>>>>>>>>>>>

#option		AUTO_DDR_CONFIG				# for DIMM recognize
option		DDR_FREQ=400
option		CORE_FREQ=500
option		HIGH_MEM_WIN_BASE_ADDR=0x80000000
option		USE_DDR_1TMODE
option		LOONGSON_GMAC

select		mod_flash_amd				# AMD flash device programming
select		mod_flash_intel				# intel flash device programming
select		mod_flash_sst				# intel flash device programming
select		mod_flash_st				# intel flash device programming
select		mod_debugger				# Debugging module
select		mod_symbols				# Symbol table handling
select		mod_s3load				# Srecord loading
#select		mod_fastload				# LSI Fastload
select		mod_elfload				# ELF loading
select		mod_txt

#
# Command selection. Selects pmon commands
#
select		cmd_newmt
select		cmd_setup
select		cmd_about				# Display info about PMON
select		cmd_boot				# Boot wrapper
select		cmd_mycmd
#select		cmd_mycmd_debug
select		cmd_newmt
select		cmd_cache				# Cache enabling
#select		cmd_call				# Call a function command
select		cmd_date				# Time of day command
select		cmd_env					# Full blown environment command set
select		cmd_flash				# Flash programming cmds
select		cmd_hist				# Command history
select		cmd_ifaddr				# Interface address command
select		cmd_l					# Disassemble
select		cmd_mem					# Memory manipulation commands
select		cmd_more				# More paginator
select		cmd_mt					# Simple memory test command
select		cmd_misc				# Reboot & Flush etc.
#select		cmd_stty				# TTY setings command
select		cmd_tr					# Host port-through command
select		cmd_devls				# Device list
select		cmd_set					# As cmd_env but not req. cmd_hist
select		cmd_testdisk
#select		cmd_test
#select		cmd_main
select		cmd_dtb
option		DTB
option		INTERFACE_3A780E
#
select		cmd_shell				# Shell commands, vers, help, eval
#
#
# Platform options
#
select		mod_uart_ns16550			# Standard UART driver
select		mod_display
option		CONS_BAUD=B115200
select		ext2
select		fatfs
select		ramfiles
#select		mod_x86emu				# X86 emulation for VGA
option		MY40IO
select		mod_x86emu_int10
option		MY61IO
option		VGA_BASE=0xb0000000
select		mod_vgacon
#select		sis315e
#option		SIS315E
option		VGA_NO_ROM				# display card has no rom
#select		mod_sisfb
option		NOPCINAMES				# Save some space for x86emu
#option		FASTBOOT
#select		vt82c686				# via686a/b code

select		mod_framebuffer
select		mod_vesa
#option		CONFIG_VIDEO_16BPP
#option		X1280x1024
#option		CONFIG_VIDEO_8BPP_INDEX
### config for feiling lcd
option		LCD_EN
option		PAI2
option		FB_XSIZE=1024
option		FB_YSIZE=600
#option		FB_XSIZE=800
#option		FB_YSIZE=480
option		CONFIG_VIDEO_16BPP
### config for st7701s lcd
#select		st7701s
#option		FB_XSIZE=480
#option		FB_YSIZE=800
#option		CONFIG_VIDEO_32BPP

#
# Functional options.
#
option		NOSNOOP					# Caches are no-snooping

#
# HAVE options. What tgt level provide
#
option		HAVE_TOD				# Time-Of-Day clock
option		INTERNAL_RTC				# chip internal RTC
#option		EXTERNAL_RTC				# external RTC
option		HPET_RTC
#option		USE_LEGACY_RTC

option		HAVE_NVENV				# Platform has non-volatile env mem
option		HAVE_LOGO				# Output splash logo

option		USE_SUPERIO_UART
#option		VESAFB
#option		GODSONEV2A
#option		LINUX_PC
#option		LONGMENG
#option		RADEON7000
#option		DEBUG_EMU_VGA
option		AUTOLOAD
#option		CONFIG_PCI0_LARGE_MEM
#option		CONFIG_PCI0_HUGE_MEM
#option		CONFIG_PCI0_GAINT_MEM
#option		LS2K500_HAVE_PCI
option		CONFIG_SLOW_PCI_FOR_BROKENDEV
option		PCIVERBOSE=5
option		PCI_INT_GPIO=86
option		CONFIG_CACHE_64K_4WAY
option		NVRAM_IN_FLASH

#
#  Now the Machine specification
#
mainbus0	at root
localbus0	at mainbus0
#fd0		at mainbus0
pcibr*		at mainbus0
#pcibr1		at mainbus0
pci*		at pcibr?
ppb*		at pci? dev ? function ?		# PCI-PCI bridges
pci*		at ppb? bus ?

lahci0		at localbus0 base 0x800000001f040000	# AHCI
ahci_sd*	at lahci0
ahci_cdrom*	at lahci0

#ahci*		at pci? dev ? function ?
#ahci_sd*	at ahci?
#ahci_cdrom*	at ahci?

#ohci*		at pci? dev ? function ?		# OHCI
#usb*		at ohci?
#lehci*		at pci? dev ? function ?		# EHCI
#usb*		at lehci?

lohci1		at localbus0 base 0x800000001f050000	# EHCI as OHCI
usb*		at lohci1

lohci0		at localbus0 base 0x800000001f058000	# OHCI
usb*		at lohci0

lxhci0		at localbus0 base 0x800000001f060000	# XHCI
xhci0		at lxhci0
usb*		at xhci0
select		mod_usb_xhci

##### USB
#uhci*		at pci? dev ? function ?
option		TEST_USB_HOST
#option		CONFIG_USB_HOTPLUG
option		USB3_USE_INTER_CLK

#### Networking Devices
syn0		at localbus0 base 0x800000001f020000
syn1		at localbus0 base 0x800000001f030000
#pcisyn0	at pci? dev ? function ?
#pcisyn1	at pci? dev ? function ?
select		gmac
option		CLOSE_GMAC_RX_DELAY
option		USE_GMAC_NUM=2
option		GMAC_USE_FLASH_MAC

igb*		at pci? dev ? function ?		# Intel 82576
select igb1

#### SCSI support
#siop*		at pci? dev ? function ?		# Symbios/NCR 53c...
#scsibus*	at siop?
#sd*		at scsibus? target ? lun ?
#cd*		at scsibus? target ? lun ?

#### Networking Devices
#gt0		at localbus? base 4
#gt1		at localbus? base 5
#gt2		at localbus? base 6

# fxp normally only used for debugging (enable/disable both)
#fxp*		at pci? dev ? function ?		# Intel 82559 Device
#inphy*		at mii? phy ?				# Intel 82555 PHYs
rtl*		at pci? dev ? function ?
#rtk*		at pci? dev ? function ?
em*		at pci? dev ? function ?
#uhci*		at pci? dev ? function ?
#ohci*		at pci? dev ? function ?
#usb*		at usbbus ?
#ohci1		at pci? dev ? function ?

select		mod_usb
select		mod_usb_storage
#select		mod_usb_uhci
select		mod_usb_ohci
#select		mod_usb_ehci
select		mod_usb_kbd

#select		pcinvme
#pcinvme*	at pci? dev ? function ?
#nvme*		at pcinvme?

#### IDE controllers
option		IDE_DMA
pciide*		at pci ? dev ? function ? flags 0x0000

#### IDE hard drives
wd*		at pciide? channel ? drive ? flags 0x0000

#### SD CARD drives
#option 	MCI_DEBUG
loopdev0	at mainbus0

tfcard0 	at localbus0 flags 0
tfcard1 	at localbus0 flags 0
select 		emmc 
select 		tfcard 
#### Pseudo devices
pseudo-device	loop	1				# network loopback

ide_cd* 	at pciide? channel ? drive ? flags 0x0001
select 		iso9660
option		IDECD
select		cmd_testfire
#option		HAVE_NB_SERIAL
#option		USE_ENVMAC
#option		LOOKLIKE_PC
#select		cmd_lwdhcp
#select		cmd_bootp
option		FOR_GXEMUL
select		ext4
select		fatfs
option		FLOATINGPT
#option		WDC_NORESET
select		gzip
option		INPUT_FROM_BOTH
option		OUTPUT_TO_BOTH
option		KBD_CHECK_FAST
option		CONFIG_VIDEO_SW_CURSOR

select		http
select		tcp
select		inet
select		nand
select		cmd_xyzmodem

#### Beep
option          HS0636

### Multiplexing configuration
#select		chip_board
select		pai_v2
#select		module
### MEM CONTROL
#option         DDR_S1=0xf8a18404           # for ls2k500 pai
option          DDR_S1=0xf9a18204           # for ls2k500 module & pai_v2
option		CONFIG_DDR_32BIT	     #for pai_v2
option         DDR_RESET_REVERT
option         TEMP_EXTREME




