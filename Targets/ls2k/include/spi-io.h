#ifndef _SPI_IO_H
#define _SPI_IO_H
#if defined(LOONGARCH_2P500)
#include "ls2p500.h"
#define USIZE_TYPE unsigned long
#elif defined(LOONGARCH_2P300)
#include "ls2p300.h"
#define USIZE_TYPE unsigned int
#elif defined(LOONGARCH_2K300)
#include "target/ls2k300.h"
#define USIZE_TYPE unsigned int
#elif defined(LOONGARCH_2K500)
#include "target/ls2k500.h"
#define USIZE_TYPE unsigned int
#else
#define USIZE_TYPE unsigned long
#endif

typedef USIZE_TYPE usize;
#endif


#define  SPI_CR1                                0x00
#define  SPI_CR2                                0x04
#define  SPI_CR3                                0x08
#define  SPI_CR4                                0x0c
#define  SPI_IER                                0x10
#define  SPI_SR1                                0x14
#define  SPI_SR2                                0x18
#define  SPI_CFG1                               0x20
#define  SPI_CFG2                               0x24
#define  SPI_CFG3                               0x28
#define  SPI_CRC1                               0x30
#define  SPI_CRC2                               0x34
#define  SPI_DR                                 0x40

/*2P500 SPICR1 bit fields*/
#define SPI_CR1_Reserved        9, 31
#define SPI_CR1_SSREV           8, 8
#define SPI_CR1_Reserved0       3, 7
#define SPI_CR1_AUTOSUS         2, 2
#define SPI_CR1_CSTART          1, 1
#define SPI_CR1_SPE		0, 0

/*2P500 SPICR2 bit fields*/
#define SPI_CR2_Reserved        16, 31
#define SPI_CR2_TXDMAEN         15, 15
#define SPI_CR2_TXFTHLV         8,  9
#define SPI_CR2_RXDMAEN         7,  7
#define SPI_CR1_Reserved0       2,  6
#define SPI_CR2_RXFTHLV         0,  1

/*2P500 SPICR3 bit fields*/
#define SPI_CR3_Reserved        16, 31
#define SPI_CR3_TSIZE           0,  15

/*2P500 SPICR4 bit fields*/
#define SPI_CR4_Reserved        16, 31
#define SPI_CTSIZE              0,  15

/*2P500 SPIIER bit fields*/
#define SPI_IER_Reserved        16, 31
#define SPI_IER_EOTIE           15, 15
#define SPI_IER_Reserved0       12, 14
#define SPI_IER_MODFIE          11, 11
#define SPI_IER_CRCEIE          10, 10
#define SPI_IER_UDRIE           9,  9
#define SPI_IER_OVRIE           8,  8
#define SPI_IER_SUSPIE          7,  7
#define SPI_IER_Reserved1       6,  6
#define SPI_IER_TXEIE           5,  5
#define SPI_IER_RXEIE           4,  4
#define SPI_IER_Reserved2	3,  3
#define SPI_IER_DXAIE           2,  2
#define SPI_IER_TXAIE           1,  1
#define SPI_IER_RXAIE           0,  0

/*2P500 SPISR1 bit fields*/
#define SPI_SR1_Reserved        16, 31
#define SPI_SR1_EOT             15, 15
#define SPI_SR1_Reserved0       12, 14
#define SPI_SR1_MODF            11, 11
#define SPI_SR1_CRCE            10, 10
#define SPI_SR1_UDR             9,  9
#define SPI_SR1_OVR             8,  8
#define SPI_SR1_SUSP            7,  7
#define SPI_SR1_Reserved1       6,  6
#define SPI_SR1_TXE             5,  5
#define SPI_SR1_RXE		4,  4
#define SPI_SR1_Reserved2       3,  3
#define SPI_SR1_DXA             2,  2
#define SPI_SR1_TXA             1,  1
#define SPI_SR1_RXA             0,  0

/*2P500 SPISR2 bit fields*/
#define SPI_SR2_Reserved        11, 31
#define SPI_SR2_TXFLV           8,  10
#define SPI_SR2_Reserved0       3,  7
#define SPI_SR2_RXFLV           0,  2

/*2P500 CFG1 bit fields*/
#define SPI_CFG1_Reserved       13, 31
#define SPI_CFG1_DSIZE          8,  12
#define SPI_CFG1_LSBFRST        7,  7
#define SPI_CFG1_Reserved0      2,  6
#define SPI_CFG1_CPHA           1,  1
#define SPI_CFG1_CPOL           0,  0

/*2P500 CFG2 bit fields*/
#define SPI_CFG2_Reserved       16, 31
#define SPI_CFG2_BRINT          8,  15
#define SPI_CFG2_BRDEC          2,  7
#define SPI_CFG2_Reserved0      0,  1

/*2P500 CFG3 bit fields*/
#define SPI_CFG3_Reserved       10, 31
#define SPI_CFG3_SSMODE         9,  8
#define SPI_CFG3_Reserved0      4,  7
#define SPI_CFG3_DOE            3,  3
#define SPI_CFG3_DIE            2,  2
#define SPI_CFG3_DIOSWP         1,  1
#define SPI_CFG3_MSTR           0,  0

#define u32    unsigned int
#define BIT(a) (1 << a)
enum spi_base{
	SPI1 =0x14203000,
	SPI2 =0x15109000,
	SPI3 =0x15203000,
};

struct ls_spi{
	enum spi_base		base;
	u32                     cur_speed;
	u32                     cur_bpw;
	u32                     cur_fthlv;
	u32			pol;
	u32			pha;

	const void              *tx_buf;
	void                    *rx_buf;
	u32                     tx_len;
	u32                     rx_len;
	u32			cur_xferlen;
	char                    cs_change;
};

struct message{
	const void              *tx_buf;
	void                    *rx_buf;
	int                     len;
	char                    cs_change;
};

static inline usize bit_field_mask(int start, int end)
{
	usize e = (1ul << end);
	usize s = (1ul << start);
	return (e - s) + e;
}

static inline void set_bit_field_v(volatile usize *reg, int start, int end, usize val)
{
	usize mask = bit_field_mask(start, end);
	*reg = (*reg & ~mask) | ((val << start) & mask);
}

static inline usize get_bit_field_v(volatile usize *reg, int start, int end)
{
	return (*reg & bit_field_mask(start, end)) >> start;
}

static  inline void spi_writel(struct ls_spi *spi,unsigned short offset, usize value)
{
        writel(value,PHYS_TO_UNCACHED(spi->base+offset));
}

static inline u32 spi_readl(struct ls_spi *spi, unsigned short offset)
{
        return readl(PHYS_TO_UNCACHED(spi->base+offset));
}

static inline void spi_set_bits(struct ls_spi *spi, unsigned short offset,int start,int end, usize val)
{
	 set_bit_field_v(PHYS_TO_UNCACHED(spi->base+offset), start, end, val);

}

static inline usize spi_get_bits(struct ls_spi *spi, usize offset, int start, int end)
{
	return get_bit_field_v(PHYS_TO_UNCACHED(spi->base+offset), start, end);
}

static inline u32 spi_get_txaflag(struct ls_spi *spi)
{
	return spi_get_bits(spi,SPI_SR1,SPI_SR1_TXA);
}

static inline u32 spi_get_rxaflag(struct ls_spi *spi)
{
	return spi_get_bits(spi,SPI_SR1,SPI_SR1_RXA);
}

static inline u32 spi_get_eotflag(struct ls_spi *spi)
{
	return spi_get_bits(spi,SPI_SR1,SPI_SR1_EOT);
}

static inline u32 spi_get_rxflv(struct ls_spi *spi)
{
	return spi_get_bits(spi,SPI_SR2,SPI_SR2_RXFLV);
}

static inline void spi_spe_enable(struct ls_spi *spi)
{
	spi_set_bits(spi,SPI_CR1,SPI_CR1_SPE,1);
}

static inline void spi_clr_over(struct ls_spi *spi)
{
	spi_set_bits(spi,SPI_SR1,SPI_SR1_OVR,1);
}

static inline void spi_spe_disable(struct ls_spi *spi)
{
	spi_set_bits(spi,SPI_CR1,SPI_CR1_SPE,0);
}

static inline void spi_autosus_enable(struct ls_spi *spi)
{
	spi_set_bits(spi,SPI_CR1,SPI_CR1_AUTOSUS,1);
}

static inline void spi_autosus_disable(struct ls_spi *spi)
{
	spi_set_bits(spi,SPI_CR1,SPI_CR1_AUTOSUS,0);
}

static inline void spi_cstart_enable(struct ls_spi *spi)
{
	spi_set_bits(spi,SPI_CR1,SPI_CR1_CSTART,1);
}

static inline void spi_set_cpha(struct ls_spi *spi,usize cpha)
{
	spi_set_bits(spi,SPI_CFG1,SPI_CFG1_CPHA,cpha);
}

static inline void spi_set_cpol(struct ls_spi *spi,usize cpol)
{
	spi_set_bits(spi,SPI_CFG1,SPI_CFG1_CPOL,cpol);
}

static inline spi_set_lsb(struct ls_spi *spi,unsigned lsb_first)
{
	spi_set_bits(spi,SPI_CFG1,SPI_CFG1_LSBFRST,lsb_first);
}

int ls_spi_transfer_poll(struct message *msg);

#endif


